// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const TutorialPage_GeneralButtonText_Next = 'TutorialPage.GeneralButtonText.Next';
  static const TutorialPage_GeneralButtonText_Skip = 'TutorialPage.GeneralButtonText.Skip';
  static const TutorialPage_GeneralButtonText_Done = 'TutorialPage.GeneralButtonText.Done';
  static const TutorialPage_GeneralButtonText = 'TutorialPage.GeneralButtonText';
  static const TutorialPage_WarningBoxTutorialDescription = 'TutorialPage.WarningBoxTutorialDescription';
  static const TutorialPage_NotificationTutorialDescription = 'TutorialPage.NotificationTutorialDescription';
  static const TutorialPage = 'TutorialPage';
  static const Home_Latest = 'Home.Latest';
  static const Home_Total = 'Home.Total';
  static const Home_photo = 'Home.photo';
  static const Home_costumDialog = 'Home.costumDialog';
  static const Home_NoPhotos = 'Home.NoPhotos';
  static const Home_FreePremium_ButtonText = 'Home.FreePremium.ButtonText';
  static const Home_FreePremium_Congratulations = 'Home.FreePremium.Congratulations';
  static const Home_FreePremium_DialogMessage = 'Home.FreePremium.DialogMessage';
  static const Home_FreePremium_DialogButtonText = 'Home.FreePremium.DialogButtonText';
  static const Home_FreePremium = 'Home.FreePremium';
  static const Home_Streak_Day = 'Home.Streak.Day';
  static const Home_Streak_Continue = 'Home.Streak.Continue';
  static const Home_Streak_StreakHistory = 'Home.Streak.StreakHistory';
  static const Home_Streak_UsedFreeze = 'Home.Streak.UsedFreeze';
  static const Home_Streak_EarnFreeze = 'Home.Streak.EarnFreeze';
  static const Home_Streak = 'Home.Streak';
  static const Home_PhotoFix_Title = 'Home.PhotoFix.Title';
  static const Home_PhotoFix_Description = 'Home.PhotoFix.Description';
  static const Home_PhotoFix_ButtonText = 'Home.PhotoFix.ButtonText';
  static const Home_PhotoFix_ProgressTitle = 'Home.PhotoFix.ProgressTitle';
  static const Home_PhotoFix_ProgressDescription = 'Home.PhotoFix.ProgressDescription';
  static const Home_PhotoFix_SuccessMessage = 'Home.PhotoFix.SuccessMessage';
  static const Home_PhotoFix_NoPhotosMessage = 'Home.PhotoFix.NoPhotosMessage';
  static const Home_PhotoFix_ErrorMessage = 'Home.PhotoFix.ErrorMessage';
  static const Home_PhotoFix = 'Home.PhotoFix';
  static const Home = 'Home';
  static const FeedPage_AppBar_Feed_Favorite = 'FeedPage.AppBar.Feed.Favorite';
  static const FeedPage_AppBar_Feed_All = 'FeedPage.AppBar.Feed.All';
  static const FeedPage_AppBar_Feed = 'FeedPage.AppBar.Feed';
  static const FeedPage_AppBar_Title = 'FeedPage.AppBar.Title';
  static const FeedPage_AppBar = 'FeedPage.AppBar';
  static const FeedPage_Body_SeeMore = 'FeedPage.Body.SeeMore';
  static const FeedPage_Body_SeeLess = 'FeedPage.Body.SeeLess';
  static const FeedPage_Body_Empty = 'FeedPage.Body.Empty';
  static const FeedPage_Body_NoDescription = 'FeedPage.Body.NoDescription';
  static const FeedPage_Body = 'FeedPage.Body';
  static const FeedPage_BottomSheet_Edit = 'FeedPage.BottomSheet.Edit';
  static const FeedPage_BottomSheet = 'FeedPage.BottomSheet';
  static const FeedPage = 'FeedPage';
  static const VideoPreferencesPage_AppBar_Title = 'VideoPreferencesPage.AppBar.Title';
  static const VideoPreferencesPage_AppBar_ExportButton = 'VideoPreferencesPage.AppBar.ExportButton';
  static const VideoPreferencesPage_AppBar = 'VideoPreferencesPage.AppBar';
  static const VideoPreferencesPage_Body_VideoFeatures_Header = 'VideoPreferencesPage.Body.VideoFeatures.Header';
  static const VideoPreferencesPage_Body_VideoFeatures_SliderQuality_Header = 'VideoPreferencesPage.Body.VideoFeatures.SliderQuality.Header';
  static const VideoPreferencesPage_Body_VideoFeatures_SliderQuality = 'VideoPreferencesPage.Body.VideoFeatures.SliderQuality';
  static const VideoPreferencesPage_Body_VideoFeatures_SliderLength_Header = 'VideoPreferencesPage.Body.VideoFeatures.SliderLength.Header';
  static const VideoPreferencesPage_Body_VideoFeatures_SliderLength = 'VideoPreferencesPage.Body.VideoFeatures.SliderLength';
  static const VideoPreferencesPage_Body_VideoFeatures_LengthInfo = 'VideoPreferencesPage.Body.VideoFeatures.LengthInfo';
  static const VideoPreferencesPage_Body_VideoFeatures = 'VideoPreferencesPage.Body.VideoFeatures';
  static const VideoPreferencesPage_Body_AudioSource_Header = 'VideoPreferencesPage.Body.AudioSource.Header';
  static const VideoPreferencesPage_Body_AudioSource_Permission = 'VideoPreferencesPage.Body.AudioSource.Permission';
  static const VideoPreferencesPage_Body_AudioSource = 'VideoPreferencesPage.Body.AudioSource';
  static const VideoPreferencesPage_Body_AddMusic = 'VideoPreferencesPage.Body.AddMusic';
  static const VideoPreferencesPage_Body = 'VideoPreferencesPage.Body';
  static const VideoPreferencesPage_AnyPhoto = 'VideoPreferencesPage.AnyPhoto';
  static const VideoPreferencesPage_Watermark = 'VideoPreferencesPage.Watermark';
  static const VideoPreferencesPage_WannaRemoveWatermark = 'VideoPreferencesPage.WannaRemoveWatermark';
  static const VideoPreferencesPage_IsIncludeNonFaces = 'VideoPreferencesPage.IsIncludeNonFaces';
  static const VideoPreferencesPage_AtLeastThreePhoto = 'VideoPreferencesPage.AtLeastThreePhoto';
  static const VideoPreferencesPage_CancelExportProgress = 'VideoPreferencesPage.CancelExportProgress';
  static const VideoPreferencesPage_VideoPropertiesList_Header = 'VideoPreferencesPage.VideoPropertiesList.Header';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_Duration = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.Duration';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_PerPhotoDuration = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.PerPhotoDuration';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_PhotoCount = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.PhotoCount';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_DayCount = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.DayCount';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_Resolution = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.Resolution';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_FirstPhoto = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.FirstPhoto';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_LastPhoto = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.LastPhoto';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_GoToFolder = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems.GoToFolder';
  static const VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems = 'VideoPreferencesPage.VideoPropertiesList.VideoPropertiesItems';
  static const VideoPreferencesPage_VideoPropertiesList = 'VideoPreferencesPage.VideoPropertiesList';
  static const VideoPreferencesPage = 'VideoPreferencesPage';
  static const VideoPreview_AppBarHeader = 'VideoPreview.AppBarHeader';
  static const VideoPreview_Done = 'VideoPreview.Done';
  static const VideoPreview = 'VideoPreview';
  static const Calendar_AppBar = 'Calendar.AppBar';
  static const Calendar = 'Calendar';
  static const PaymentPage_BecomeProMemeber = 'PaymentPage.BecomeProMemeber';
  static const PaymentPage_Package_Popular = 'PaymentPage.Package.Popular';
  static const PaymentPage_Package_DiscountWord = 'PaymentPage.Package.DiscountWord';
  static const PaymentPage_Package_Annual = 'PaymentPage.Package.Annual';
  static const PaymentPage_Package_Monthly = 'PaymentPage.Package.Monthly';
  static const PaymentPage_Package_Month = 'PaymentPage.Package.Month';
  static const PaymentPage_Package_Year = 'PaymentPage.Package.Year';
  static const PaymentPage_Package = 'PaymentPage.Package';
  static const PaymentPage_Features_Feature1 = 'PaymentPage.Features.Feature1';
  static const PaymentPage_Features_Feature2 = 'PaymentPage.Features.Feature2';
  static const PaymentPage_Features_Feature3 = 'PaymentPage.Features.Feature3';
  static const PaymentPage_Features_Feature4 = 'PaymentPage.Features.Feature4';
  static const PaymentPage_Features_Feature5 = 'PaymentPage.Features.Feature5';
  static const PaymentPage_Features = 'PaymentPage.Features';
  static const PaymentPage_tryFreeTrials = 'PaymentPage.tryFreeTrials';
  static const PaymentPage_FetchingProducts = 'PaymentPage.FetchingProducts';
  static const PaymentPage_legalText = 'PaymentPage.legalText';
  static const PaymentPage_billedmonthly = 'PaymentPage.billedmonthly';
  static const PaymentPage_billedyearly = 'PaymentPage.billedyearly';
  static const PaymentPage_TheMarketNotAvailable = 'PaymentPage.TheMarketNotAvailable';
  static const PaymentPage = 'PaymentPage';
  static const CameraPages_AlreadyTook = 'CameraPages.AlreadyTook';
  static const CameraPages_Camera_TakePhoto = 'CameraPages.Camera.TakePhoto';
  static const CameraPages_Camera_LocationService_LocationLoading = 'CameraPages.Camera.LocationService.LocationLoading';
  static const CameraPages_Camera_LocationService_Unknown = 'CameraPages.Camera.LocationService.Unknown';
  static const CameraPages_Camera_LocationService_AddLocation = 'CameraPages.Camera.LocationService.AddLocation';
  static const CameraPages_Camera_LocationService_AccessLocationDialog_OpenCamera = 'CameraPages.Camera.LocationService.AccessLocationDialog.OpenCamera';
  static const CameraPages_Camera_LocationService_AccessLocationDialog_ExplanationOfPermission = 'CameraPages.Camera.LocationService.AccessLocationDialog.ExplanationOfPermission';
  static const CameraPages_Camera_LocationService_AccessLocationDialog_GoSettingsButton = 'CameraPages.Camera.LocationService.AccessLocationDialog.GoSettingsButton';
  static const CameraPages_Camera_LocationService_AccessLocationDialog = 'CameraPages.Camera.LocationService.AccessLocationDialog';
  static const CameraPages_Camera_LocationService_Tutorial_ChangeCamera = 'CameraPages.Camera.LocationService.Tutorial.ChangeCamera';
  static const CameraPages_Camera_LocationService_Tutorial_ChangeCameraDesc = 'CameraPages.Camera.LocationService.Tutorial.ChangeCameraDesc';
  static const CameraPages_Camera_LocationService_Tutorial_AutoTake = 'CameraPages.Camera.LocationService.Tutorial.AutoTake';
  static const CameraPages_Camera_LocationService_Tutorial_AutoTakeDesc = 'CameraPages.Camera.LocationService.Tutorial.AutoTakeDesc';
  static const CameraPages_Camera_LocationService_Tutorial_GhostImage = 'CameraPages.Camera.LocationService.Tutorial.GhostImage';
  static const CameraPages_Camera_LocationService_Tutorial_GhostImageDesc = 'CameraPages.Camera.LocationService.Tutorial.GhostImageDesc';
  static const CameraPages_Camera_LocationService_Tutorial_ReferanceParts = 'CameraPages.Camera.LocationService.Tutorial.ReferanceParts';
  static const CameraPages_Camera_LocationService_Tutorial_ReferancePartsDesc = 'CameraPages.Camera.LocationService.Tutorial.ReferancePartsDesc';
  static const CameraPages_Camera_LocationService_Tutorial_Grid = 'CameraPages.Camera.LocationService.Tutorial.Grid';
  static const CameraPages_Camera_LocationService_Tutorial_GridDesc = 'CameraPages.Camera.LocationService.Tutorial.GridDesc';
  static const CameraPages_Camera_LocationService_Tutorial = 'CameraPages.Camera.LocationService.Tutorial';
  static const CameraPages_Camera_LocationService = 'CameraPages.Camera.LocationService';
  static const CameraPages_Camera_PhotoPreview_Header = 'CameraPages.Camera.PhotoPreview.Header';
  static const CameraPages_Camera_PhotoPreview_Confirm = 'CameraPages.Camera.PhotoPreview.Confirm';
  static const CameraPages_Camera_PhotoPreview_AddNote = 'CameraPages.Camera.PhotoPreview.AddNote';
  static const CameraPages_Camera_PhotoPreview_Tutorial_Note = 'CameraPages.Camera.PhotoPreview.Tutorial.Note';
  static const CameraPages_Camera_PhotoPreview_Tutorial_NoteDesc = 'CameraPages.Camera.PhotoPreview.Tutorial.NoteDesc';
  static const CameraPages_Camera_PhotoPreview_Tutorial_Location = 'CameraPages.Camera.PhotoPreview.Tutorial.Location';
  static const CameraPages_Camera_PhotoPreview_Tutorial_LocationDesc = 'CameraPages.Camera.PhotoPreview.Tutorial.LocationDesc';
  static const CameraPages_Camera_PhotoPreview_Tutorial_Statistics = 'CameraPages.Camera.PhotoPreview.Tutorial.Statistics';
  static const CameraPages_Camera_PhotoPreview_Tutorial_StatisticsDesc = 'CameraPages.Camera.PhotoPreview.Tutorial.StatisticsDesc';
  static const CameraPages_Camera_PhotoPreview_Tutorial_ReferanceParts = 'CameraPages.Camera.PhotoPreview.Tutorial.ReferanceParts';
  static const CameraPages_Camera_PhotoPreview_Tutorial = 'CameraPages.Camera.PhotoPreview.Tutorial';
  static const CameraPages_Camera_PhotoPreview = 'CameraPages.Camera.PhotoPreview';
  static const CameraPages_Camera_GhostImage = 'CameraPages.Camera.GhostImage';
  static const CameraPages_Camera_RefParts = 'CameraPages.Camera.RefParts';
  static const CameraPages_Camera_ErrorCamera = 'CameraPages.Camera.ErrorCamera';
  static const CameraPages_Camera_SaveFaceParts = 'CameraPages.Camera.SaveFaceParts';
  static const CameraPages_Camera_SetReferanceFace = 'CameraPages.Camera.SetReferanceFace';
  static const CameraPages_Camera_RefPartsDescTutorial = 'CameraPages.Camera.RefPartsDescTutorial';
  static const CameraPages_Camera_FaceDetection_FaceNotDetected = 'CameraPages.Camera.FaceDetection.FaceNotDetected';
  static const CameraPages_Camera_FaceDetection_FaceDetectionError = 'CameraPages.Camera.FaceDetection.FaceDetectionError';
  static const CameraPages_Camera_FaceDetection_PhotoNotCropped = 'CameraPages.Camera.FaceDetection.PhotoNotCropped';
  static const CameraPages_Camera_FaceDetection_Statistics = 'CameraPages.Camera.FaceDetection.Statistics';
  static const CameraPages_Camera_FaceDetection_Rotation = 'CameraPages.Camera.FaceDetection.Rotation';
  static const CameraPages_Camera_FaceDetection_Size = 'CameraPages.Camera.FaceDetection.Size';
  static const CameraPages_Camera_FaceDetection_Distance = 'CameraPages.Camera.FaceDetection.Distance';
  static const CameraPages_Camera_FaceDetection_Side = 'CameraPages.Camera.FaceDetection.Side';
  static const CameraPages_Camera_FaceDetection_RightLeft = 'CameraPages.Camera.FaceDetection.RightLeft';
  static const CameraPages_Camera_FaceDetection_UpDown = 'CameraPages.Camera.FaceDetection.UpDown';
  static const CameraPages_Camera_FaceDetection = 'CameraPages.Camera.FaceDetection';
  static const CameraPages_Camera = 'CameraPages.Camera';
  static const CameraPages = 'CameraPages';
  static const PhotoPage_NoDescripiton = 'PhotoPage.NoDescripiton';
  static const PhotoPage = 'PhotoPage';
  static const SettingsPage_HeaderSettings = 'SettingsPage.HeaderSettings';
  static const SettingsPage_HeaderAbout = 'SettingsPage.HeaderAbout';
  static const SettingsPage_SettingsTiles_DailyReminder_Header = 'SettingsPage.SettingsTiles.DailyReminder.Header';
  static const SettingsPage_SettingsTiles_DailyReminder_TimePiclerText = 'SettingsPage.SettingsTiles.DailyReminder.TimePiclerText';
  static const SettingsPage_SettingsTiles_DailyReminder_InvalidDateTime = 'SettingsPage.SettingsTiles.DailyReminder.InvalidDateTime';
  static const SettingsPage_SettingsTiles_DailyReminder_AccessNotificaitonDialog = 'SettingsPage.SettingsTiles.DailyReminder.AccessNotificaitonDialog';
  static const SettingsPage_SettingsTiles_DailyReminder = 'SettingsPage.SettingsTiles.DailyReminder';
  static const SettingsPage_SettingsTiles_Theme_Header = 'SettingsPage.SettingsTiles.Theme.Header';
  static const SettingsPage_SettingsTiles_Theme_SelectColor_Title = 'SettingsPage.SettingsTiles.Theme.SelectColor.Title';
  static const SettingsPage_SettingsTiles_Theme_SelectColor_SelectedColors = 'SettingsPage.SettingsTiles.Theme.SelectColor.SelectedColors';
  static const SettingsPage_SettingsTiles_Theme_SelectColor_Colors_Main = 'SettingsPage.SettingsTiles.Theme.SelectColor.Colors.Main';
  static const SettingsPage_SettingsTiles_Theme_SelectColor_Colors_Light = 'SettingsPage.SettingsTiles.Theme.SelectColor.Colors.Light';
  static const SettingsPage_SettingsTiles_Theme_SelectColor_Colors_Dark = 'SettingsPage.SettingsTiles.Theme.SelectColor.Colors.Dark';
  static const SettingsPage_SettingsTiles_Theme_SelectColor_Colors = 'SettingsPage.SettingsTiles.Theme.SelectColor.Colors';
  static const SettingsPage_SettingsTiles_Theme_SelectColor = 'SettingsPage.SettingsTiles.Theme.SelectColor';
  static const SettingsPage_SettingsTiles_Theme = 'SettingsPage.SettingsTiles.Theme';
  static const SettingsPage_SettingsTiles_Language_Header = 'SettingsPage.SettingsTiles.Language.Header';
  static const SettingsPage_SettingsTiles_Language_SelectingPopUp_Title = 'SettingsPage.SettingsTiles.Language.SelectingPopUp.Title';
  static const SettingsPage_SettingsTiles_Language_SelectingPopUp_Languages_English = 'SettingsPage.SettingsTiles.Language.SelectingPopUp.Languages.English';
  static const SettingsPage_SettingsTiles_Language_SelectingPopUp_Languages_Turkish = 'SettingsPage.SettingsTiles.Language.SelectingPopUp.Languages.Turkish';
  static const SettingsPage_SettingsTiles_Language_SelectingPopUp_Languages = 'SettingsPage.SettingsTiles.Language.SelectingPopUp.Languages';
  static const SettingsPage_SettingsTiles_Language_SelectingPopUp = 'SettingsPage.SettingsTiles.Language.SelectingPopUp';
  static const SettingsPage_SettingsTiles_Language = 'SettingsPage.SettingsTiles.Language';
  static const SettingsPage_SettingsTiles_Storage_AppBarTitle = 'SettingsPage.SettingsTiles.Storage.AppBarTitle';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description_Title = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxImportPhotos.description.Title';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description_Content = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxImportPhotos.description.Content';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxImportPhotos.description';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_ButtonText = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxImportPhotos.ButtonText';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_CancelImportProgress = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxImportPhotos.CancelImportProgress';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxImportPhotos';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_description_Title = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.description.Title';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_description_Content = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.description.Content';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_description = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.description';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_ButtonText = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.ButtonText';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp_Title = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.SurePopUp.Title';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp_Content = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.SurePopUp.Content';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp_ActionButtons_No = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.SurePopUp.ActionButtons.No';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp_ActionButtons_Yes = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.SurePopUp.ActionButtons.Yes';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp_ActionButtons = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.SurePopUp.ActionButtons';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos.SurePopUp';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxExportPhotos';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_description_Title = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.description.Title';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_description_Content = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.description.Content';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_description = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.description';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_FirstButtonText = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.FirstButtonText';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SecondButtonText = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SecondButtonText';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_Title = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp.Title';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_MessageAll = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp.MessageAll';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_MessageImported = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp.MessageImported';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_ActionButtons_No = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp.ActionButtons.No';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_ActionButtons_timerAccept = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp.ActionButtons.timerAccept';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_ActionButtons_Yes = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp.ActionButtons.Yes';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_ActionButtons = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp.ActionButtons';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.SurePopUp';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_PermanentlyDeleteWarning = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos.PermanentlyDeleteWarning';
  static const SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos = 'SettingsPage.SettingsTiles.Storage.Body.WarningBoxDeleteAllPhotos';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_Title = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.Title';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_NoDateWarning = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.NoDateWarning';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AnyPhotoSelected = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.AnyPhotoSelected';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AccesStorageRequestWarning = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.AccesStorageRequestWarning';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AccesPhotosRequestWarning = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.AccesPhotosRequestWarning';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_DateNotFound = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.DateNotFound';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_UndatedPhotosSet = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.UndatedPhotosSet';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_YouMustAnalyze = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.YouMustAnalyze';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager_Error = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager.Error';
  static const SettingsPage_SettingsTiles_Storage_Body_PhotoManager = 'SettingsPage.SettingsTiles.Storage.Body.PhotoManager';
  static const SettingsPage_SettingsTiles_Storage_Body = 'SettingsPage.SettingsTiles.Storage.Body';
  static const SettingsPage_SettingsTiles_Storage = 'SettingsPage.SettingsTiles.Storage';
  static const SettingsPage_SettingsTiles_Orientation_Title = 'SettingsPage.SettingsTiles.Orientation.Title';
  static const SettingsPage_SettingsTiles_Orientation_Reccomendation = 'SettingsPage.SettingsTiles.Orientation.Reccomendation';
  static const SettingsPage_SettingsTiles_Orientation_Save = 'SettingsPage.SettingsTiles.Orientation.Save';
  static const SettingsPage_SettingsTiles_Orientation_OrientatiionPopUp_Warning = 'SettingsPage.SettingsTiles.Orientation.OrientatiionPopUp.Warning';
  static const SettingsPage_SettingsTiles_Orientation_OrientatiionPopUp_Content = 'SettingsPage.SettingsTiles.Orientation.OrientatiionPopUp.Content';
  static const SettingsPage_SettingsTiles_Orientation_OrientatiionPopUp = 'SettingsPage.SettingsTiles.Orientation.OrientatiionPopUp';
  static const SettingsPage_SettingsTiles_Orientation = 'SettingsPage.SettingsTiles.Orientation';
  static const SettingsPage_SettingsTiles_Supporters = 'SettingsPage.SettingsTiles.Supporters';
  static const SettingsPage_SettingsTiles_RateUsBottomSheet_Header = 'SettingsPage.SettingsTiles.RateUsBottomSheet.Header';
  static const SettingsPage_SettingsTiles_RateUsBottomSheet_LaterButton = 'SettingsPage.SettingsTiles.RateUsBottomSheet.LaterButton';
  static const SettingsPage_SettingsTiles_RateUsBottomSheet = 'SettingsPage.SettingsTiles.RateUsBottomSheet';
  static const SettingsPage_SettingsTiles_Contact_ContactUsHeader = 'SettingsPage.SettingsTiles.Contact.ContactUsHeader';
  static const SettingsPage_SettingsTiles_Contact_AskAQuestion = 'SettingsPage.SettingsTiles.Contact.AskAQuestion';
  static const SettingsPage_SettingsTiles_Contact_ReportAProblem = 'SettingsPage.SettingsTiles.Contact.ReportAProblem';
  static const SettingsPage_SettingsTiles_Contact_IdeasAndFeedback = 'SettingsPage.SettingsTiles.Contact.IdeasAndFeedback';
  static const SettingsPage_SettingsTiles_Contact = 'SettingsPage.SettingsTiles.Contact';
  static const SettingsPage_SettingsTiles_About_Header = 'SettingsPage.SettingsTiles.About.Header';
  static const SettingsPage_SettingsTiles_About_TermsOfService = 'SettingsPage.SettingsTiles.About.TermsOfService';
  static const SettingsPage_SettingsTiles_About_PrivacyPolicy = 'SettingsPage.SettingsTiles.About.PrivacyPolicy';
  static const SettingsPage_SettingsTiles_About = 'SettingsPage.SettingsTiles.About';
  static const SettingsPage_SettingsTiles_ShareUs = 'SettingsPage.SettingsTiles.ShareUs';
  static const SettingsPage_SettingsTiles = 'SettingsPage.SettingsTiles';
  static const SettingsPage_Supporters_Thanking = 'SettingsPage.Supporters.Thanking';
  static const SettingsPage_Supporters_InternetConnection = 'SettingsPage.Supporters.InternetConnection';
  static const SettingsPage_Supporters_Error = 'SettingsPage.Supporters.Error';
  static const SettingsPage_Supporters_NoSupporters = 'SettingsPage.Supporters.NoSupporters';
  static const SettingsPage_Supporters_Closing = 'SettingsPage.Supporters.Closing';
  static const SettingsPage_Supporters = 'SettingsPage.Supporters';
  static const SettingsPage_version = 'SettingsPage.version';
  static const SettingsPage = 'SettingsPage';
  static const PhotoEvents_Delete = 'PhotoEvents.Delete';
  static const PhotoEvents_Download = 'PhotoEvents.Download';
  static const PhotoEvents_Share = 'PhotoEvents.Share';
  static const PhotoEvents_ScaffoldMessage_photoDeleted = 'PhotoEvents.ScaffoldMessage.photoDeleted';
  static const PhotoEvents_ScaffoldMessage_photoCouldntDeleted = 'PhotoEvents.ScaffoldMessage.photoCouldntDeleted';
  static const PhotoEvents_ScaffoldMessage_AllPhotosDownloaded = 'PhotoEvents.ScaffoldMessage.AllPhotosDownloaded';
  static const PhotoEvents_ScaffoldMessage_PhotoDownloaded = 'PhotoEvents.ScaffoldMessage.PhotoDownloaded';
  static const PhotoEvents_ScaffoldMessage_YouDontHaveAnyPhoto = 'PhotoEvents.ScaffoldMessage.YouDontHaveAnyPhoto';
  static const PhotoEvents_ScaffoldMessage_YouDontHaveAnyImportedPhoto = 'PhotoEvents.ScaffoldMessage.YouDontHaveAnyImportedPhoto';
  static const PhotoEvents_ScaffoldMessage_AllPhotosDeleted = 'PhotoEvents.ScaffoldMessage.AllPhotosDeleted';
  static const PhotoEvents_ScaffoldMessage_ImportedPhotosDeleted = 'PhotoEvents.ScaffoldMessage.ImportedPhotosDeleted';
  static const PhotoEvents_ScaffoldMessage_AddedFavorites = 'PhotoEvents.ScaffoldMessage.AddedFavorites';
  static const PhotoEvents_ScaffoldMessage_RemovedFavorites = 'PhotoEvents.ScaffoldMessage.RemovedFavorites';
  static const PhotoEvents_ScaffoldMessage = 'PhotoEvents.ScaffoldMessage';
  static const PhotoEvents_LocationService_ButtonText = 'PhotoEvents.LocationService.ButtonText';
  static const PhotoEvents_LocationService_ForwardMap = 'PhotoEvents.LocationService.ForwardMap';
  static const PhotoEvents_LocationService_FailForwarding = 'PhotoEvents.LocationService.FailForwarding';
  static const PhotoEvents_LocationService = 'PhotoEvents.LocationService';
  static const PhotoEvents = 'PhotoEvents';
  static const RateUsDialogWarnings_RateUsInternetConnectionFail = 'RateUsDialogWarnings.RateUsInternetConnectionFail';
  static const RateUsDialogWarnings_message_Dictation = 'RateUsDialogWarnings.message.Dictation';
  static const RateUsDialogWarnings_message_Subject = 'RateUsDialogWarnings.message.Subject';
  static const RateUsDialogWarnings_message_Body = 'RateUsDialogWarnings.message.Body';
  static const RateUsDialogWarnings_message = 'RateUsDialogWarnings.message';
  static const RateUsDialogWarnings = 'RateUsDialogWarnings';
  static const Dialog_Okay = 'Dialog.Okay';
  static const Dialog_Cancel = 'Dialog.Cancel';
  static const Dialog_ShareFacelog = 'Dialog.ShareFacelog';
  static const Dialog_TakeMorePhotoPremium = 'Dialog.TakeMorePhotoPremium';
  static const Dialog_MustPremium = 'Dialog.MustPremium';
  static const Dialog = 'Dialog';
  static const Message_Warning = 'Message.Warning';
  static const Message_Info = 'Message.Info';
  static const Message_Success = 'Message.Success';
  static const Message = 'Message';
  static const General_NoteBottomSheetNoteSomethingHintText = 'General.NoteBottomSheetNoteSomethingHintText';
  static const General_AppError = 'General.AppError';
  static const General_AccesRequestWarning = 'General.AccesRequestWarning';
  static const General = 'General';
  static const VideoListPage_title = 'VideoListPage.title';
  static const VideoListPage_dialogMessage = 'VideoListPage.dialogMessage';
  static const VideoListPage_approve = 'VideoListPage.approve';
  static const VideoListPage_selectVideo = 'VideoListPage.selectVideo';
  static const VideoListPage_noVideo = 'VideoListPage.noVideo';
  static const VideoListPage_DeleteSuccessfully = 'VideoListPage.DeleteSuccessfully';
  static const VideoListPage_DeleteUnsuccessfully = 'VideoListPage.DeleteUnsuccessfully';
  static const VideoListPage = 'VideoListPage';
  static const PremiumVerificationError = 'PremiumVerificationError';
  static const AddMusicPage_Title = 'AddMusicPage.Title';
  static const AddMusicPage_DoneButton = 'AddMusicPage.DoneButton';
  static const AddMusicPage_minutesShortage = 'AddMusicPage.minutesShortage';
  static const AddMusicPage_secondShortage = 'AddMusicPage.secondShortage';
  static const AddMusicPage_warning = 'AddMusicPage.warning';
  static const AddMusicPage = 'AddMusicPage';
  static const ProWarning = 'ProWarning';

}
