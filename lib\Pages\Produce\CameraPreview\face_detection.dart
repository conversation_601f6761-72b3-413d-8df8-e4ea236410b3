import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/camera_mixin.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_statistics_container.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image/image.dart' as img;
import 'package:image_editor/image_editor.dart';
import 'dart:ui' as ui;

bool isLoadingProgress = false;
Face? currentFace;
Offset? photoCenter;

final FaceDetectorOptions options = FaceDetectorOptions(
  enableLandmarks: true,
  enableTracking: false,
  enableClassification: false,
  enableContours: false,
  performanceMode: FaceDetectorMode.fast,
);
final FaceDetector faceDetector = FaceDetector(options: options);

/// Landscape modda çekilen fotoğrafları döndürür
Future<void> _rotateImageIfLandscape({required String imagePath, required int angle}) async {
  if (isLandscape) {
    final File file = File(imagePath);
    Uint8List? imageBytes = await file.readAsBytes();

    final ImageEditorOption option = ImageEditorOption();
    option.addOption(RotateOption(angle));

    imageBytes = await ImageEditor.editImage(
      image: imageBytes,
      imageEditorOption: option,
    );

    await file.writeAsBytes(imageBytes!);
  }
}

Future<void> photoDetectFace({String? photoPath}) async {
  try {
    isLoadingProgress = true;

    // Landscape modda çekilen fotoğrafları düzelt (face detection için)
    if (photoPath == null) {
      await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: -90);
    }

    late final img.Image decodedImage;
    late final Uint8List imageBytes;

    // Orijinal kaliteyi korumak için sıkıştırma yapmıyoruz
    final File imageFile = File(photoPath ?? cameraPhoto!.path);
    imageBytes = await imageFile.readAsBytes();
    decodedImage = img.decodeImage(imageBytes)!;
    photoCenter = Offset(decodedImage.width / 2, decodedImage.height / 2);

    final List<Face> faces = await faceDetector.processImage(
      InputImage.fromFile(File(photoPath ?? cameraPhoto!.path)),
    );

    if (faces.isNotEmpty) {
      if (faces.length > 1) {
        // Birden fazla yüz varsa, fotoğrafın merkezine en yakın olanını seç
        double minDistance = double.infinity;

        for (var face in faces) {
          double distance = (face.boundingBox.center - photoCenter!).distance;
          if (distance < minDistance) {
            minDistance = distance;
            currentFace = face;
          }
        }
      } else {
        currentFace = faces.first;
      }

      await cropPhoto(
        faceDetails: currentFace,
        photoPath: photoPath ?? cameraPhoto!.path,
        imageBytes: imageBytes,
        decodedImage: decodedImage,
      );

      // Fotoğrafı orijinal durumuna döndür
      if (photoPath == null) {
        await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: 90);
      }
    } else {
      if (photoPath == null) {
        Helper().getMessage(
          message: LocaleKeys.CameraPages_Camera_FaceDetection_FaceNotDetected.tr(),
          status: StatusEnum.WARNING,
        );
      }

      currentFace = null;

      // Fotoğrafı orijinal durumuna döndür
      if (photoPath == null) {
        await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: 90);
      }
    }

    faceDetector.close();

    if (currentFace != null) {
      calculateTotalPercentage();
    } else {
      totalAccuracy = null;
    }

    isLoadingProgress = false;
  } catch (e) {
    if (photoPath == null) {
      Helper().getMessage(
        message: LocaleKeys.CameraPages_Camera_FaceDetection_FaceDetectionError.tr(),
        status: StatusEnum.WARNING,
      );
    }
    debugPrint(e.toString());
    faceDetector.close();
    isLoadingProgress = false;
  }
}

Future<void> cropPhoto({
  required Face? faceDetails,
  required String photoPath,
  required Uint8List imageBytes,
  required img.Image decodedImage,
}) async {
  croppedPhotoIndexForLoading.value++;
  if (faceDetails == null) return;

  try {
    final croppedImage = await _processCropWithImageEditor(
      faceDetails: faceDetails,
      photoPath: photoPath,
      decodedImage: decodedImage,
      imageBytes: imageBytes,
    );

    // Kırpılan fotoğrafı dosyaya kaydet
    await File(photoPath).writeAsBytes(croppedImage);
  } catch (e) {
    Helper().getMessage(
      message: LocaleKeys.CameraPages_Camera_FaceDetection_PhotoNotCropped.tr(),
      status: StatusEnum.WARNING,
    );
  }
}

Future<Uint8List> _processCropWithImageEditor({
  required Face faceDetails,
  required String photoPath,
  required Uint8List imageBytes,
  required img.Image decodedImage,
}) async {
  // Gözlerin konumunu al
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Yüz tespit edilemedi.");
  }

  // Gözler arasındaki mesafeyi hesapla
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));

  // Gözlerin orta noktasını bul
  final eyeCenter = Offset(
    (leftEye.x + rightEye.x) / 2,
    // Y ekseninde gözlerin ortalamasından biraz aşağıya (burnun üstü)
    ((leftEye.y + rightEye.y) / 2) + 100,
  );

  // Gözlerin eğim açısını hesapla (derece cinsinden)
  final angle = math.atan2(leftEye.y - rightEye.y, leftEye.x - rightEye.x) * 180 / math.pi;

  // Kırpma işlemleri
  int cropLeft = (decodedImage.width / 2) < eyeCenter.dx ? ((eyeCenter.dx - (decodedImage.width / 2)) * 2).toInt() : 0;
  int cropRight = (decodedImage.width / 2) > eyeCenter.dx ? (((decodedImage.width / 2) - eyeCenter.dx) * 2).toInt() : 0;
  int cropTop = (decodedImage.height / 2) < eyeCenter.dy ? ((eyeCenter.dy - (decodedImage.height / 2)) * 2).toInt() : 0;
  int cropBottom = (decodedImage.height / 2) > eyeCenter.dy ? (((decodedImage.height / 2) - eyeCenter.dy) * 2).toInt() : 0;

  // Boyutlandırma işlemi - gözler arası mesafeye göre ölçeklendirme
  final double refToNewRatio = 220 / eyeDistance; // 220px ideal gözler arası mesafe

  // ImageEditorOption oluştur
  final option = ImageEditorOption();

  // Kırpma işlemi
  option.addOption(
    ClipOption(
      x: cropLeft,
      y: cropTop,
      width: decodedImage.width - cropLeft - cropRight,
      height: decodedImage.height - cropTop - cropBottom,
    ),
  );

  final int editedWidth = ((decodedImage.width - cropLeft - cropRight) * refToNewRatio).toInt();
  final int editedHeight = ((decodedImage.height - cropTop - cropBottom) * refToNewRatio).toInt();

  // Boyutlandırma işlemi
  option.addOption(
    ScaleOption(
      editedWidth,
      editedHeight,
    ),
  );

  // Döndürme işlemi - yüzü düzelt
  option.addOption(
    RotateOption(
      -angle.toInt() + 180, // Açıyı düzelt ve normalize et
    ),
  );

  // İşlemleri uygula
  Uint8List editedImageBytes = (await ImageEditor.editImage(
    image: imageBytes,
    imageEditorOption: option,
  ))!;

  editedImageBytes = await mergeBackground(
    editedImageBytes: editedImageBytes,
    canvasSize: Size(decodedImage.width.toDouble(), decodedImage.height.toDouble()),
  );

  return editedImageBytes;
}

/// Kırpılan fotoğrafı siyah arka plan üzerinde ortalar
Future<Uint8List> mergeBackground({
  required Uint8List editedImageBytes,
  required Size canvasSize,
}) async {
  final ui.Image inputImage = await decodeImageFromList(editedImageBytes);

  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);

  // Siyah arka plan çiz
  canvas.drawRect(Offset.zero & canvasSize, Paint()..color = Colors.black);

  // Resmi yerleştir
  double width = inputImage.width.toDouble();
  double height = inputImage.height.toDouble();
  double x = (canvasSize.width - width) / 2;
  double y = (canvasSize.height - height) / 2;

  // Orijinal görüntüyü ortala ve oranını koruyarak çiz
  canvas.drawImageRect(
    inputImage,
    Rect.fromLTWH(0, 0, inputImage.width.toDouble(), inputImage.height.toDouble()),
    Rect.fromLTWH(x, y, width, height),
    Paint(),
  );

  final picture = recorder.endRecording();
  final finalImage = await picture.toImage(canvasSize.width.toInt(), canvasSize.height.toInt());

  // ui.Image'i img.Image'e dönüştür
  final byteData = await finalImage.toByteData(format: ui.ImageByteFormat.rawRgba);
  final pixels = byteData!.buffer.asUint8List();
  final image = img.Image.fromBytes(
    width: finalImage.width,
    height: finalImage.height,
    bytes: pixels.buffer,
    numChannels: 4,
  );

  // JPEG olarak kodla
  final jpegBytes = img.encodeJpg(image, quality: 90);

  return Uint8List.fromList(jpegBytes);
}

/// Yüz hizalama doğruluğunu hesapla
void calculateTotalPercentage() {
  double accuracyPercentage({
    required double value,
    required double acceptableDifference,
    required double fitValue,
  }) {
    double error = (fitValue - value).abs() / acceptableDifference;
    double percentage = 100 - (error * 10);

    if (percentage > 100) {
      percentage = 100;
    } else if (percentage < 0) {
      percentage = 0;
    }

    return percentage;
  }

  // Yüz rotasyon ve pozisyon değerleri
  final double headRotationDifferanceSide = currentFace!.headEulerAngleZ!;
  final double headRotationDifferanceRightLeft = currentFace!.headEulerAngleY!;
  final double headRotationDifferanceTopBottom = currentFace!.headEulerAngleX!;
  final double boxSizeDifferent = Offset(
    currentFace!.boundingBox.size.height,
    currentFace!.boundingBox.size.width,
  ).distance;
  final double boxDistanceToCenter = Helper().calculateTwoPointDistance(
    currentFace!.boundingBox.center,
    photoCenter!,
  );

  // Kabul edilebilir fark değerleri
  const double acceptableDifferenceSide = 3;
  const double acceptableDifferenceRightLeft = 3;
  const double acceptableDifferenceTopBottom = 2;
  const double acceptableDifferenceSize = 30;
  const double acceptableDifferenceDistance = 30;

  // İdeal değerler
  const double fitValueSide = 0;
  const double fitValueRightLeft = 0;
  const double fitValueTopBottom = -12;
  const double fitValueSize = 950;
  const double fitValueDistance = 0;

  // Doğruluk yüzdelerini hesapla
  final sideRatio = accuracyPercentage(
    value: headRotationDifferanceSide,
    acceptableDifference: acceptableDifferenceSide,
    fitValue: fitValueSide,
  );
  final rightLeftRatio = accuracyPercentage(
    value: headRotationDifferanceRightLeft,
    acceptableDifference: acceptableDifferenceRightLeft,
    fitValue: fitValueRightLeft,
  );
  final topBottomRatio = accuracyPercentage(
    value: headRotationDifferanceTopBottom,
    acceptableDifference: acceptableDifferenceTopBottom,
    fitValue: fitValueTopBottom,
  );
  final sizeRatio = accuracyPercentage(
    value: boxSizeDifferent,
    acceptableDifference: acceptableDifferenceSize,
    fitValue: fitValueSize,
  );
  final distanceRatio = accuracyPercentage(
    value: boxDistanceToCenter,
    acceptableDifference: acceptableDifferenceDistance,
    fitValue: fitValueDistance,
  );

  // Toplam doğruluk yüzdesini hesapla
  totalAccuracy = (sideRatio + rightLeftRatio + topBottomRatio + sizeRatio + distanceRatio) / 5;
}
